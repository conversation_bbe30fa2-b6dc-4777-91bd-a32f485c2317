/**
 * KPI Filtering Utilities
 * Provides client-side filtering for different dashboard types
 */

export interface KPIData {
  id: string
  name: string
  name_ar?: string
  current_value: number
  target_value?: number
  unit: string
  status: string
  trend: any
  achievement_percentage?: number
  last_updated?: string
  category?: string
  metric_type?: string
}

export type DashboardType = 'executive' | 'hr' | 'financial' | 'department' | 'project' | 'personal' | 'learning'

/**
 * Filter KPIs based on dashboard type
 */
export function filterKPIsByDashboardType(kpis: KPIData[], dashboardType: DashboardType): KPIData[] {
  if (!kpis || kpis.length === 0) {
    return []
  }

  switch (dashboardType) {
    case 'hr':
      return kpis.filter(kpi => 
        isHRKPI(kpi)
      )
    
    case 'financial':
      return kpis.filter(kpi => 
        isFinancialKPI(kpi)
      )
    
    case 'department':
      return kpis.filter(kpi => 
        isDepartmentKPI(kpi)
      )
    
    case 'project':
      return kpis.filter(kpi => 
        isProjectKPI(kpi)
      )
    
    case 'executive':
    default:
      // Executive dashboard shows all KPIs
      return kpis
  }
}

/**
 * Check if KPI is HR-related
 */
function isHRKPI(kpi: KPIData): boolean {
  const name = kpi.name?.toLowerCase() || ''
  const nameAr = kpi.name_ar?.toLowerCase() || ''
  const category = kpi.category?.toLowerCase() || ''
  const metricType = kpi.metric_type?.toLowerCase() || ''

  const hrKeywords = [
    'employee', 'موظف', 'موظفين',
    'turnover', 'دوران',
    'attendance', 'حضور',
    'satisfaction', 'رضا',
    'training', 'تدريب',
    'performance', 'أداء',
    'retention', 'احتفاظ',
    'hr', 'الموارد البشرية'
  ]

  return hrKeywords.some(keyword => 
    name.includes(keyword) || 
    nameAr.includes(keyword) || 
    category.includes(keyword)
  ) || metricType === 'employee'
}

/**
 * Check if KPI is Financial-related
 */
function isFinancialKPI(kpi: KPIData): boolean {
  const name = kpi.name?.toLowerCase() || ''
  const nameAr = kpi.name_ar?.toLowerCase() || ''
  const category = kpi.category?.toLowerCase() || ''
  const metricType = kpi.metric_type?.toLowerCase() || ''

  const financialKeywords = [
    'revenue', 'إيرادات',
    'cash', 'نقدي', 'تدفق',
    'profit', 'ربح',
    'cost', 'تكلفة',
    'budget', 'ميزانية',
    'financial', 'مالي',
    'invoice', 'فاتورة',
    'expense', 'مصروف',
    'income', 'دخل'
  ]

  return financialKeywords.some(keyword => 
    name.includes(keyword) || 
    nameAr.includes(keyword) || 
    category.includes(keyword)
  ) || metricType === 'financial'
}

/**
 * Check if KPI is Department-related
 */
function isDepartmentKPI(kpi: KPIData): boolean {
  const name = kpi.name?.toLowerCase() || ''
  const nameAr = kpi.name_ar?.toLowerCase() || ''
  const category = kpi.category?.toLowerCase() || ''
  const metricType = kpi.metric_type?.toLowerCase() || ''

  const departmentKeywords = [
    'department', 'قسم',
    'team', 'فريق',
    'productivity', 'إنتاجية',
    'utilization', 'استخدام',
    'efficiency', 'كفاءة'
  ]

  return departmentKeywords.some(keyword => 
    name.includes(keyword) || 
    nameAr.includes(keyword) || 
    category.includes(keyword)
  ) || metricType === 'department'
}

/**
 * Check if KPI is Project-related
 */
function isProjectKPI(kpi: KPIData): boolean {
  const name = kpi.name?.toLowerCase() || ''
  const nameAr = kpi.name_ar?.toLowerCase() || ''
  const category = kpi.category?.toLowerCase() || ''
  const metricType = kpi.metric_type?.toLowerCase() || ''

  const projectKeywords = [
    'project', 'مشروع',
    'delivery', 'تسليم',
    'completion', 'إنجاز',
    'milestone', 'معلم',
    'deadline', 'موعد'
  ]

  return projectKeywords.some(keyword => 
    name.includes(keyword) || 
    nameAr.includes(keyword) || 
    category.includes(keyword)
  ) || metricType === 'project'
}

/**
 * Get dashboard title based on type and language
 */
export function getDashboardTitle(dashboardType: DashboardType, language: 'ar' | 'en'): string {
  const titles = {
    executive: {
      ar: 'لوحة التحكم التنفيذية',
      en: 'Executive Dashboard'
    },
    hr: {
      ar: 'تحليلات الموارد البشرية',
      en: 'HR Analytics'
    },
    financial: {
      ar: 'التحليلات المالية',
      en: 'Financial Analytics'
    },
    department: {
      ar: 'تحليلات الأقسام',
      en: 'Department Analytics'
    },
    project: {
      ar: 'تحليلات المشاريع',
      en: 'Project Analytics'
    },
    personal: {
      ar: 'لوحة التحكم الشخصية',
      en: 'Personal Dashboard'
    },
    learning: {
      ar: 'تحليلات التعلم',
      en: 'Learning Analytics'
    }
  }

  return titles[dashboardType]?.[language] || titles.executive[language]
}

/**
 * Transform KPI data to ensure Arabic support
 */
export function transformKPIWithArabicSupport(kpi: any, language: 'ar' | 'en' = 'en'): KPIData {
  return {
    id: kpi.id?.toString() || Math.random().toString(),
    name: language === 'ar' ? (kpi.name_ar || kpi.name || 'مؤشر غير معروف') : (kpi.name || 'Unknown KPI'),
    name_ar: kpi.name_ar || kpi.name || 'مؤشر غير معروف',
    current_value: kpi.current_value ?? kpi.value ?? 0,
    target_value: kpi.target_value ?? null,
    unit: kpi.unit || '',
    status: kpi.status || 'Unknown',
    trend: kpi.trend || { direction: 'stable', change_percentage: 0 },
    achievement_percentage: kpi.achievement_percentage ?? calculateAchievementPercentage(kpi),
    last_updated: kpi.last_updated || kpi.updated_at,
    category: kpi.category || kpi.metric_type || 'General',
    metric_type: kpi.metric_type
  }
}

/**
 * Calculate achievement percentage
 */
function calculateAchievementPercentage(kpi: any): number {
  const current = kpi.current_value ?? kpi.value ?? 0
  const target = kpi.target_value ?? null
  
  if (!target || target === 0) {
    return 0
  }
  
  return Math.round((current / target) * 100)
}
