/**
 * Hierarchical KPI Dashboard Component
 * Enterprise-grade KPI dashboard with role-based access control and hierarchical data filtering
 * 
 * Features:
 * - Role-specific KPI displays
 * - Hierarchical data access patterns
 * - Manager-subordinate data visibility
 * - Real-time updates with role context
 * - Department and project scoped KPIs
 */

import React, { useState, useEffect, useMemo } from 'react'
import { 
  BarChart3, 
  TrendingUp, 
  Users, 
  Building, 
  Target, 
  Activity,
  AlertTriangle,
  RefreshCw,
  Eye,
  EyeOff,
  Filter,
  Download
} from 'lucide-react'
import { useHierarchicalAccessRedux } from '../../hooks/useHierarchicalAccessRedux'
import { useAuth } from '../../hooks/useAuth'
import { apiClient } from '../../services/api'
import logger from '../../utils/logger'
import { filterKPIsByDashboardType, getDashboardTitle, transformKPIWithArabicSupport, type KPIData } from '../../utils/kpiFilters'

// Using KPIData interface from utils/kpiFilters.ts

interface HierarchicalKPIDashboardProps {
  dashboardType?: 'executive' | 'hr' | 'financial' | 'department' | 'project' | 'personal' | 'learning'
  className?: string
  kpiFilters?: {
    categories?: string[]
    kpiType?: string
    specificKPIs?: string[]
  }
}

const HierarchicalKPIDashboard: React.FC<HierarchicalKPIDashboardProps> = ({
  dashboardType = 'executive',
  className = '',
  kpiFilters
}) => {
  const { user } = useAuth()
  const {
    accessInfo,
    isManager,
    hasHierarchicalAccess,
    canAccessAllData,
    isDepartmentScoped,
    isProjectScoped,
    kpiCategories,
    roleBasedKPIFilters,
    loading: accessLoading,
    error: accessError,
    refreshAccessInfo
  } = useHierarchicalAccessRedux()

  // State
  const [kpiData, setKpiData] = useState<KPIData[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null)
  const [selectedCategories, setSelectedCategories] = useState<string[]>([])
  const [showFilters, setShowFilters] = useState(false)

  // Dashboard configuration based on role and type
  const dashboardConfig = useMemo(() => {
    const configs = {
      executive: {
        title: 'Executive Dashboard',
        titleAr: 'لوحة التحكم التنفيذية',
        icon: BarChart3,
        categories: ['Financial', 'Operational', 'Strategic', 'HR'],
        color: 'from-purple-500 to-blue-500'
      },
      hr: {
        title: 'HR Analytics',
        titleAr: 'تحليلات الموارد البشرية',
        icon: Users,
        categories: ['HR', 'Employee', 'Retention', 'Satisfaction'],
        color: 'from-green-500 to-teal-500'
      },
      financial: {
        title: 'Financial Analytics',
        titleAr: 'التحليلات المالية',
        icon: TrendingUp,
        categories: ['Financial', 'Revenue', 'Cost', 'Budget'],
        color: 'from-yellow-500 to-orange-500'
      },
      department: {
        title: 'Department Analytics',
        titleAr: 'تحليلات القسم',
        icon: Building,
        categories: ['Department', 'Team', 'Productivity'],
        color: 'from-blue-500 to-indigo-500'
      },
      project: {
        title: 'Project Analytics',
        titleAr: 'تحليلات المشاريع',
        icon: Target,
        categories: ['Project', 'Timeline', 'Resource', 'Quality'],
        color: 'from-indigo-500 to-purple-500'
      },
      personal: {
        title: 'Personal Analytics',
        titleAr: 'التحليلات الشخصية',
        icon: Activity,
        categories: ['Personal', 'Individual', 'Performance'],
        color: 'from-pink-500 to-rose-500'
      },
      learning: {
        title: 'Learning Analytics',
        titleAr: 'تحليلات التعلم',
        icon: Activity,
        categories: ['Learning', 'Development', 'Skills'],
        color: 'from-cyan-500 to-blue-500'
      }
    }
    return configs[dashboardType] || configs.executive
  }, [dashboardType])

  // Load KPI data with hierarchical filtering
  const loadKPIData = async () => {
    try {
      setLoading(true)
      setError(null)

      // Build filters based on hierarchical access and dashboard type
      let filters: any = {
        categories: selectedCategories.length > 0 ? selectedCategories : (kpiFilters?.categories || dashboardConfig.categories),
        role_based: true,
        dashboard_type: dashboardType
      }

      // Add specific KPI filtering if provided
      if (kpiFilters?.specificKPIs && kpiFilters.specificKPIs.length > 0) {
        filters.specific_kpis = kpiFilters.specificKPIs.join(',')
      }

      // Add KPI type filtering
      if (kpiFilters?.kpiType) {
        filters.kpi_type = kpiFilters.kpiType
      }

      // Use category-specific API endpoints for better filtering
      let apiEndpoint = '/kpi-metrics/'
      if (dashboardType === 'hr') {
        apiEndpoint = '/kpi/dashboard/hr/'
        filters.category_filter = 'Human Resources,HR,Employee,Retention,Satisfaction'
      } else if (dashboardType === 'financial') {
        apiEndpoint = '/kpi/dashboard/financial/'
        filters.category_filter = 'Financial,Revenue,Cost,Budget,Profitability'
      } else if (dashboardType === 'department') {
        filters.category_filter = 'Department,Team,Productivity,Resource'
      }

      // Add role-based filters if available, otherwise use fallback
      if (roleBasedKPIFilters) {
        filters = { ...filters, ...roleBasedKPIFilters }
        logger.info('hierarchicalKPI', 'Using role-based KPI filters', { filters })
      } else {
        // Fallback: Load all KPIs for the dashboard type without role restrictions
        logger.warn('hierarchicalKPI', 'Role-based filters not available, using fallback', { dashboardType })
        filters.fallback_mode = true
      }

      logger.info('hierarchicalKPI', 'Attempting to load KPI data', {
        filters,
        dashboardType,
        role: accessInfo?.user_role,
        hasRoleFilters: !!roleBasedKPIFilters
      })

      const response = await apiClient.get('/kpi-metrics/', { params: filters })
      const apiData = response.data as any

      // Transform API response to match expected format
      const transformedResponse = {
        success: true,
        data: apiData.results || apiData,
        error: null
      }

      logger.info('hierarchicalKPI', 'KPI API response', {
        success: transformedResponse.success,
        dataType: typeof transformedResponse.data,
        dataIsArray: Array.isArray(transformedResponse.data),
        dataLength: Array.isArray(transformedResponse.data) ? transformedResponse.data.length : 'N/A',
        dataKeys: transformedResponse.data ? Object.keys(transformedResponse.data) : 'N/A',
        error: transformedResponse.error,
        dashboardType,
        fullResponse: transformedResponse
      })

      if (transformedResponse.success && transformedResponse.data) {
        // Handle different response structures
        let kpiArray: any[] = []

        if (Array.isArray(transformedResponse.data)) {
          kpiArray = transformedResponse.data
        } else if (transformedResponse.data.results && Array.isArray(transformedResponse.data.results)) {
          // Paginated response
          kpiArray = transformedResponse.data.results
        } else if (transformedResponse.data.data && Array.isArray(transformedResponse.data.data)) {
          // Nested data structure
          kpiArray = transformedResponse.data.data
        } else {
          logger.error('hierarchicalKPI', 'Unexpected response structure', { response: transformedResponse })
          throw new Error('Unexpected response structure: data is not an array')
        }

        // Transform and filter KPI data based on dashboard type
        const transformedKPIs = kpiArray.map(kpi => transformKPIWithArabicSupport(kpi, 'ar'))
        const filteredKPIs = filterKPIsByDashboardType(transformedKPIs, dashboardType)

        setKpiData(filteredKPIs)
        setLastUpdated(new Date())

        logger.info('hierarchicalKPI', 'KPI data loaded and filtered successfully', {
          originalCount: kpiArray.length,
          filteredCount: filteredKPIs.length,
          dashboardType,
          role: accessInfo?.user_role,
          kpiNames: filteredKPIs.map(kpi => kpi.name).slice(0, 5), // First 5 KPI names for debugging
          sampleKPI: filteredKPIs[0] // Log first KPI structure for debugging
        })

        // Debug: Log to console for immediate visibility
        console.log('🎯 FILTERED KPI DATA LOADED:', {
          originalCount: kpiArray.length,
          filteredCount: filteredKPIs.length,
          dashboardType,
          firstKPI: filteredKPIs[0]?.name,
          firstKPIArabic: filteredKPIs[0]?.name_ar,
          firstValue: filteredKPIs[0]?.current_value,
          source: 'Real API with filtering and Arabic support'
        })
      } else {
        throw new Error('Failed to load KPI data')
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load KPI data'
      setError(errorMessage)
      logger.error('hierarchicalKPI', 'Error loading KPI data:', err)
    } finally {
      setLoading(false)
    }
  }

  // Load data when filters change
  useEffect(() => {
    // Always try to load KPI data, even if role-based filters are not available
    loadKPIData()
  }, [roleBasedKPIFilters, selectedCategories, dashboardType])

  // Also load data when access info becomes available (fallback trigger)
  useEffect(() => {
    if (accessInfo && kpiData.length === 0 && !loading) {
      logger.info('hierarchicalKPI', 'Access info available, triggering KPI data load', {
        role: accessInfo.user_role,
        hasRoleFilters: !!roleBasedKPIFilters
      })
      loadKPIData()
    }
  }, [accessInfo, kpiData.length, loading])

  // Handle category filter changes
  const handleCategoryToggle = (category: string) => {
    setSelectedCategories(prev => 
      prev.includes(category)
        ? prev.filter(c => c !== category)
        : [...prev, category]
    )
  }

  // Refresh data
  const handleRefresh = async () => {
    await Promise.all([
      refreshAccessInfo(),
      loadKPIData()
    ])
  }

  // Render KPI card
  const renderKPICard = (kpi: any) => {
    // Handle real database data - current_value is a number from the API
    const currentValue = kpi.current_value ?? 0
    const targetValue = kpi.target_value ?? kpi.target ?? null
    const unit = kpi.unit ?? ''
    const categoryName = kpi.category_name ?? kpi.category ?? 'Unknown'

    // Handle trend data with multiple fallback paths
    const trendData = kpi.trend
    const trendDirection = trendData?.direction ?? kpi.trend_direction?.toLowerCase() ?? kpi.trend ?? 'stable'
    const changePercentage = trendData?.change_percentage ?? kpi.change_percentage ?? 0

    // Safety check for missing data
    if (!kpi.name) {
      logger.warn('hierarchicalKPI', 'KPI missing name field', { kpi })
      return null
    }

    const trendIcon = trendDirection === 'up' ? '↗️' : trendDirection === 'down' ? '↘️' : '➡️'
    const trendColor = trendDirection === 'up' ? 'text-green-400' : trendDirection === 'down' ? 'text-red-400' : 'text-gray-400'

    return (
      <div key={kpi.id} className="bg-white/5 backdrop-blur-sm rounded-lg p-6 border border-white/10 hover:border-white/20 transition-all duration-200">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-white truncate">{kpi.name}</h3>
          <span className="text-xs text-white/60 bg-white/10 px-2 py-1 rounded-full">
            {categoryName}
          </span>
        </div>

        <div className="flex items-end justify-between mb-3">
          <div>
            <div className="text-3xl font-bold text-white mb-1">
              {typeof currentValue === 'number' ? currentValue.toLocaleString() : currentValue} {unit}
            </div>
            {targetValue && (
              <div className="text-sm text-white/60">
                Target: {typeof targetValue === 'number' ? targetValue.toLocaleString() : targetValue} {unit}
              </div>
            )}
          </div>
          <div className={`flex items-center space-x-1 ${trendColor}`}>
            <span className="text-lg">{trendIcon}</span>
            <span className="text-sm font-medium">
              {changePercentage > 0 ? '+' : ''}{typeof changePercentage === 'number' ? changePercentage.toFixed(1) : '0.0'}%
            </span>
          </div>
        </div>
        
        <div className="text-xs text-white/50">
          Updated: {kpi.current_value?.recorded_at
            ? new Date(kpi.current_value.recorded_at).toLocaleString()
            : kpi.last_updated
              ? new Date(kpi.last_updated).toLocaleString()
              : kpi.updated_at
                ? new Date(kpi.updated_at).toLocaleString()
                : 'Unknown'
          }
        </div>
      </div>
    )
  }

  if (accessLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="flex items-center space-x-2 text-white/60">
          <RefreshCw className="h-5 w-5 animate-spin" />
          <span>Loading access information...</span>
        </div>
      </div>
    )
  }

  // Only show access error for critical errors, not for "Request already in progress"
  if (accessError && !accessError.includes('Request already in progress')) {
    return (
      <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-6">
        <div className="flex items-center space-x-2 text-red-300 mb-2">
          <AlertTriangle className="h-5 w-5" />
          <span className="font-semibold">Access Error</span>
        </div>
        <p className="text-red-200 text-sm mb-4">{accessError}</p>
        <button
          onClick={refreshAccessInfo}
          className="px-4 py-2 bg-red-500/20 text-red-300 rounded-lg hover:bg-red-500/30 transition-colors"
        >
          Retry
        </button>
      </div>
    )
  }

  const DashboardIcon = dashboardConfig.icon

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className={`p-3 rounded-lg bg-gradient-to-r ${dashboardConfig.color}`}>
            <DashboardIcon className="h-6 w-6 text-white" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-white">{getDashboardTitle(dashboardType, 'ar')}</h1>
            {accessInfo && (
              <p className="text-white/60 text-sm">
                {accessInfo.user_role} • {isManager ? 'Manager Access' : 'Individual Access'}
                {hasHierarchicalAccess && ` • ${accessInfo.accessible_data.employees_count} employees`}
              </p>
            )}
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setShowFilters(!showFilters)}
            className={`p-2 rounded-lg transition-colors ${
              showFilters ? 'bg-blue-500/20 text-blue-300' : 'bg-white/10 text-white/60 hover:bg-white/20'
            }`}
          >
            <Filter className="h-5 w-5" />
          </button>
          <button
            onClick={handleRefresh}
            disabled={loading}
            className="p-2 bg-white/10 text-white/60 rounded-lg hover:bg-white/20 transition-colors disabled:opacity-50"
          >
            <RefreshCw className={`h-5 w-5 ${loading ? 'animate-spin' : ''}`} />
          </button>
        </div>
      </div>

      {/* Filters */}
      {showFilters && (
        <div className="bg-white/5 backdrop-blur-sm rounded-lg p-4 border border-white/10">
          <h3 className="text-lg font-semibold text-white mb-3">KPI Categories</h3>
          <div className="flex flex-wrap gap-2">
            {kpiCategories.map(category => (
              <button
                key={category}
                onClick={() => handleCategoryToggle(category)}
                className={`px-3 py-1 rounded-full text-sm transition-colors ${
                  selectedCategories.includes(category)
                    ? 'bg-blue-500/20 text-blue-300 border border-blue-500/30'
                    : 'bg-white/10 text-white/60 hover:bg-white/20'
                }`}
              >
                {category}
              </button>
            ))}
          </div>
        </div>
      )}

      {/* KPI Grid */}
      {loading ? (
        <div className="flex items-center justify-center h-64">
          <div className="flex items-center space-x-2 text-white/60">
            <RefreshCw className="h-5 w-5 animate-spin" />
            <span>Loading KPI data...</span>
          </div>
        </div>
      ) : error ? (
        <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-6">
          <div className="flex items-center space-x-2 text-red-300 mb-2">
            <AlertTriangle className="h-5 w-5" />
            <span className="font-semibold">Error Loading KPIs</span>
          </div>
          <p className="text-red-200 text-sm mb-4">{error}</p>
          <button
            onClick={loadKPIData}
            className="px-4 py-2 bg-red-500/20 text-red-300 rounded-lg hover:bg-red-500/30 transition-colors"
          >
            Retry
          </button>
        </div>
      ) : kpiData.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-white/40 mb-4">
            <BarChart3 className="h-12 w-12 mx-auto" />
          </div>
          <h3 className="text-lg font-semibold text-white/60 mb-2">No KPIs Available</h3>
          <p className="text-white/40 text-sm mb-4">
            No KPI data available for your current role and access level.
          </p>

          {/* Debug Information */}
          <div className="mt-6 p-4 bg-white/5 rounded-lg text-left text-xs text-white/60">
            <h4 className="font-semibold mb-2">Debug Information:</h4>
            <div className="space-y-1">
              <p>• Role: {accessInfo?.user_role || 'Unknown'}</p>
              <p>• Is Manager: {isManager ? 'Yes' : 'No'}</p>
              <p>• Dashboard Type: {dashboardType}</p>
              <p>• Has Role Filters: {roleBasedKPIFilters ? 'Yes' : 'No'}</p>
              <p>• Access Loading: {accessLoading ? 'Yes' : 'No'}</p>
              <p>• Access Error: {accessError || 'None'}</p>
              <p>• KPI Categories: {kpiCategories?.join(', ') || 'None'}</p>
              <p>• Selected Categories: {selectedCategories.join(', ') || 'None'}</p>
              <p>• Dashboard Categories: {dashboardConfig.categories.join(', ')}</p>
            </div>
            <button
              onClick={loadKPIData}
              className="mt-3 px-3 py-1 bg-blue-500/20 text-blue-300 rounded text-xs hover:bg-blue-500/30"
            >
              Retry Load KPIs
            </button>
          </div>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {kpiData.map(renderKPICard)}
        </div>
      )}

      {/* Footer */}
      {lastUpdated && (
        <div className="text-center text-xs text-white/40">
          Last updated: {lastUpdated.toLocaleString()}
        </div>
      )}
    </div>
  )
}

export default HierarchicalKPIDashboard
